# ES备份功能完整分析文档

## 文档概述
本文档详细分析了ES备份创建和ES备份绑定两个核心功能的接口、脚本、实现逻辑和调用关系。

**文档创建时间**: 2025-09-23 13:07:04  
**分析人员**: hongdong.xie

## 1. 功能架构概览

### 1.1 模块边界与依赖
- **访问入口**: Django路由 `/spider/es_mgt/` → `es_mgt.urls` → DRF `DefaultRouter` → `backups` 资源
- **控制层**: `ESBackupView` 提供REST操作，依赖全局认证与统一响应结构 `NewApiResult`
- **服务层**: `ESBackupService` 负责与数据库、环境配置和Elasticsearch HTTP接口交互
- **数据层**: `EsMgtDumpInfo`、`EsMgtDumpBizBind` 两张业务表
- **外部系统**: Elasticsearch快照API (`_snapshot`、`_cat/indices`) 和S3兼容存储仓库

### 1.2 核心数据模型
- **EsMgtDumpInfo**: 存储备份元数据，状态字段取值 PENDING、SUCCESS、FAILED
- **EsMgtDumpBizBind**: 建立业务编码、测试迭代分支与备份的一对一绑定关系

## 2. ES备份创建功能

### 2.1 接口定义
**路径**: `POST /spider/es_mgt/backups/create/`

**请求参数**:
```json
{
    "suite_code": "it29",                    // 环境唯一编码，必填
    "source_es_module_name": "Elasticsearch7", // ES模块名称，必填
    "indices": "*,-.*",                      // 待备份索引，默认值
    "creator": "user.name",                  // 创建人，必填
    "remark": "备份说明",                     // 备注，必填
    "biz_code": "optional_biz_code",         // 业务编码，可选
    "biz_test_iter_br": "optional_branch"    // 业务测试迭代分支，可选
}
```

**响应格式**:
```json
{
    "code": 0,
    "message": "ES备份流程启动成功",
    "data": {
        "es_dump_name": "http://es-host:9200_it29-20231027103000"
    }
}
```

### 2.2 核心实现逻辑

#### 2.2.1 控制层处理 (ESBackupView.create_backup)
```python
@action(methods=['post'], detail=False, url_path='create')
def create_backup(self, request):
    # 1. 参数校验
    # 2. 调用服务层创建备份
    success, message, es_dump_name = self.backup_service.create_backup(...)
    # 3. 返回HTTP 202状态
```

#### 2.2.2 服务层处理 (ESBackupService.create_backup)
**完整流程**:
1. **获取ES节点地址**: 通过环境绑定表获取对应模块的ES节点地址
2. **验证/创建快照仓库**: 调用 `verify_or_create_repository` 方法
3. **生成快照名称**: 格式为 `{suite_code}-{timestamp}`
4. **创建快照**: 调用Elasticsearch `_snapshot` 接口异步创建
5. **统计索引数量**: 获取非系统索引数量用于后续验证
6. **数据库事务操作**:
   - 创建 `EsMgtDumpInfo` 记录
   - 如果提供业务信息，创建 `EsMgtDumpBizBind` 绑定关系

#### 2.2.3 关键方法详解

**快照创建方法**:
```python
def create_snapshot(self, es_host: str, snapshot_name: str, indices: str = "*") -> Tuple[bool, str]:
    snapshot_url = f"http://{es_host}/_snapshot/{self.repository_name}/{snapshot_name}?wait_for_completion=false"
    snapshot_config = {
        "indices": indices,
        "ignore_unavailable": True,
        "include_global_state": False
    }
    response = requests.put(snapshot_url, json=snapshot_config, ...)
```

**仓库验证方法**:
```python
def verify_or_create_repository(self, es_host: str) -> Tuple[bool, str]:
    # 1. 检查仓库是否存在
    # 2. 如果不存在则创建S3仓库
    # 3. 验证仓库可用性
```

### 2.3 状态管理机制

#### 2.3.1 备份状态更新接口
**路径**: `POST /spider/es_mgt/backups/status`

**处理逻辑**:
1. 解析 `es_dump_name` 获取ES主机和快照名称
2. 调用ES接口获取快照状态
3. 状态映射规则:
   - ES状态为SUCCESS时，比较索引数量判断最终状态
   - IN_PROGRESS → RUNNING
   - FAILED → FAILED
4. 更新数据库记录

## 3. ES备份绑定功能

### 3.1 接口定义
**路径**: `POST /spider/es_mgt/backups/bind`

**请求参数**:
```json
{
    "biz_code": "my-project",                // 业务编码，必填
    "biz_test_iter_br": "feature-branch-01", // 业务测试迭代分支，必填
    "es_dump_name": "http://es-host:9200_it29-20231027103000", // 备份名称，必填
    "operator": "user.name",                 // 操作人，必填
    "source_es_module_name": "Elasticsearch7" // ES模块名，可选
}
```

### 3.2 实现逻辑

#### 3.2.1 控制层处理 (ESBackupView.create_biz_binding)
```python
@action(methods=['post'], detail=False, url_path='bind')
def create_biz_binding(self, request):
    # 1. 参数校验
    # 2. 检查唯一键冲突
    biz_test_iter_id = f"{biz_code}_{biz_test_iter_br}"
    if EsMgtDumpBizBind.objects.filter(
        biz_test_iter_id=biz_test_iter_id,
        source_es_module_name=source_es_module_name
    ).exists():
        return 冲突错误
    # 3. 调用服务层创建绑定
```

#### 3.2.2 服务层处理 (ESBackupService.create_biz_binding)
```python
def create_biz_binding(self, biz_code: str, biz_test_iter_br: str, 
                      es_dump_name: str, operator: str, 
                      source_es_module_name: str) -> Tuple[bool, str]:
    # 1. 验证备份记录存在
    # 2. 创建绑定记录
    EsMgtDumpBizBind.objects.create(
        biz_code=biz_code,
        biz_test_iter_br=biz_test_iter_br,
        biz_test_iter_id=f"{biz_code}_{biz_test_iter_br}",
        source_es_module_name=source_es_module_name,
        es_dump_name=es_dump_name,
        operator=operator
    )
```

## 4. 查询功能

### 4.1 备份列表查询
**路径**: `GET /spider/es_mgt/backups/list?suite_code=it29`

**实现**:
```python
def get_backups_by_suite_code(self, suite_code: str) -> List[str]:
    backups = EsMgtDumpInfo.objects.filter(
        suite_code=suite_code
    ).values_list('es_dump_name', flat=True).order_by('-create_time')
    return list(backups)
```

## 5. 相关脚本与自动化

### 5.1 Jenkins集成脚本

#### 5.1.1 备份恢复脚本
**文件**: `be-scripts/test_publish_aio/test_suite_init_impl_es.py`

**核心处理器**:
- `EsBackupVerifyHandler`: 验证快照完整性
- `EsDataClearHandler`: 清空ES数据
- `EsBackupRestoreHandler`: 执行快照恢复

#### 5.1.2 Jenkins流水线
**文件**: `Jenkinsfile/test_suite_init.groovy`

**ES处理阶段**:
1. ES_HEALTH_CHECK - 健康检查
2. ES_BACKUP_VERIFY - 备份验证
3. ES_DATA_CLEAR - 数据清空
4. ES_BACKUP_RESTORE - 备份恢复
5. ES_SCRIPT_EXEC - 脚本执行

### 5.2 定时任务脚本
**文件**: `be-scripts/job/cron/delete_jenkins_workspace.py`
- 清理过期的Jenkins工作空间

## 6. 数据库表结构

### 6.1 EsMgtDumpInfo (ES备份元数据表)
```sql
CREATE TABLE es_mgt_dump_info (
    id INT AUTO_INCREMENT PRIMARY KEY,
    suite_code VARCHAR(50) NOT NULL COMMENT '环境唯一编码',
    es_dump_name VARCHAR(100) NOT NULL COMMENT '备份名称',
    source_es_module_name VARCHAR(100) NOT NULL COMMENT '备份源ES模块名',
    status VARCHAR(20) DEFAULT 'PENDING' COMMENT '状态',
    repository_name VARCHAR(100) NOT NULL COMMENT 'ES快照仓库名',
    index_count INT COMMENT '索引数量',
    creator VARCHAR(50) NOT NULL COMMENT '创建人',
    remark VARCHAR(255) COMMENT '备注',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### 6.2 EsMgtDumpBizBind (业务ES备份关系表)
```sql
CREATE TABLE es_mgt_dump_biz_bind (
    id INT AUTO_INCREMENT PRIMARY KEY,
    biz_code VARCHAR(100) NOT NULL COMMENT '业务编码',
    biz_test_iter_br VARCHAR(100) NOT NULL COMMENT '业务测试迭代分支',
    biz_test_iter_id VARCHAR(201) NOT NULL COMMENT '业务测试迭代ID',
    source_es_module_name VARCHAR(100) NOT NULL COMMENT '备份源ES模块名',
    es_dump_name VARCHAR(100) NOT NULL COMMENT '关联的备份名称',
    operator VARCHAR(50) NOT NULL COMMENT '操作人',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY uk_biz_module (biz_test_iter_id, source_es_module_name)
);
```

## 7. 错误处理与监控

### 7.1 常见错误场景
1. **ES节点不可达**: 返回连接失败错误
2. **快照仓库不存在**: 自动创建S3仓库
3. **索引数量不匹配**: 状态标记为FAILED
4. **唯一键冲突**: 绑定关系已存在

### 7.2 日志记录
- 所有关键操作都有详细的日志记录
- 错误信息存储在 `error_log` 字段中
- 支持通过日志追踪完整的备份流程

## 8. 扩展建议

### 8.1 性能优化
1. 增加备份进度查询接口
2. 支持并行备份多个索引
3. 添加备份大小限制和预估

### 8.2 功能增强
1. 支持增量备份
2. 添加备份保留策略
3. 支持跨环境备份迁移
4. 增加备份完整性校验

---

**文档版本**: v1.0  
**最后更新**: 2025-09-23 13:07:04
